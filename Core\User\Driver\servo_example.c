/**
 * @file servo_example.c
 * @brief 舵机驱动使用示例
 */

#include "servo_driver.h"
#include "tim.h" // 假设您的定时器头文件

// 舵机实例
Servo_t steering_servo; // 转向舵机

/**
 * @brief 舵机初始化示例
 */
void Servo_Example_Init(void) {
    // 配置舵机参数（以TIM3_CH1为例）
    Servo_Config_t servo_config = {
        .htim = &htim3,         // 使用TIM3
        .channel = TIM_CHANNEL_1, // 通道1
        .min_pulse = 500,       // 最小脉宽值(对应0度)
        .max_pulse = 2500,      // 最大脉宽值(对应180度)
        .min_angle = 0.0f,      // 最小角度
        .max_angle = 180.0f     // 最大角度
    };
    
    // 初始化舵机
    Servo_Init(&steering_servo, &servo_config);
    
    // 启动PWM
    Servo_Start(&steering_servo);
    
    // 设置初始角度为90度（中位）
    Servo_SetAngle(&steering_servo, 90.0f);
}

/**
 * @brief 舵机控制示例
 */
void Servo_Example_Control(void) {
    // 转到0度
    Servo_SetAngle(&steering_servo, 0.0f);
    HAL_Delay(1000);
    
    // 转到90度
    Servo_SetAngle(&steering_servo, 90.0f);
    HAL_Delay(1000);
    
    // 转到180度
    Servo_SetAngle(&steering_servo, 180.0f);
    HAL_Delay(1000);
    
    // 获取当前角度
    float current = Servo_GetAngle(&steering_servo);
    // current 现在为 180.0
}
