#ifndef __SERVO_DRIVER_H__
#define __SERVO_DRIVER_H__

#include "tim.h"

/* 舵机配置 */
typedef struct {
  TIM_HandleTypeDef *htim; // 定时器句柄
  uint32_t channel;        // PWM通道
  uint32_t min_pulse;      // 最小脉宽值(对应最小角度)
  uint32_t max_pulse;      // 最大脉宽值(对应最大角度)
  float min_angle;         // 最小角度
  float max_angle;         // 最大角度
} Servo_Config_t;

typedef struct {
  Servo_Config_t config; // 舵机配置
  float current_angle;   // 当前角度
} Servo_t;

/* API */
void Servo_Init(Servo_t *servo, Servo_Config_t *config); // 初始化舵机
void Servo_SetAngle(Servo_t *servo, float angle); // 设置舵机角度
float Servo_GetAngle(Servo_t *servo); // 获取当前角度
void Servo_Start(Servo_t *servo); // 启动舵机PWM
void Servo_Stop(Servo_t *servo); // 停止舵机PWM

#endif // __SERVO_DRIVER_H__