#ifndef __MY_DEFINE_H
#define __MY_DEFINE_H

/* 标准库文件 */
#include "stdbool.h"
#include "stdarg.h"
#include "stdio.h"
#include "string.h"
#include "math.h"
#include "stdlib.h"
/* HAL库 头文件 */
#include "main.h"
#include "tim.h"
#include "usart.h"
// #include "i2c.h"

/* 组件库头文件 */
#include "ebtn.h"
#include "Emm_V5.h"
#include "pid.h"
// #include "oled.h"

// #include "inv_mpu.h"
// #include "inv_mpu_dmp_motion_driver.h"
// #include "mpu6050.h"
// #include "mpu6050_driver.h"

// #include "motor_driver.h"
// #include "encoder_driver.h"
#include "servo_driver.h"
/* 应用层头文件 */
// #include "app_oled.h"
#include "key.h"
#include "app_uart.h"
#include "app_servo.h"
#include "app_motor.h"
#include "app_pid.h"
// #include "app_pid.h"
// #include "app_encoder.h"

/* 调度器 */
#include "scheduler.h"

/* 声明 */
extern uint8_t uart1_rx_buffer[];
extern uint8_t uart1_rx_index;

extern uint8_t uart5_rx_buffer[];
extern uint8_t uart5_rx_index;

extern uint32_t lunkuo_point[][2];
extern uint32_t laser_point[][2];

extern PID_Controller pid_x, pid_y;
#endif // __MY_DEFINE_H
