#include "servo_driver.h"
#include <math.h>

/**
 * @brief 初始化舵机
 * @param servo 舵机结构体指针
 * @param config 舵机配置参数
 */
void Servo_Init(Servo_t *servo, Servo_Config_t *config) {
    servo->config = *config; // 复制配置参数
    servo->current_angle = config->min_angle; // 初始角度设为最小角度
}

/**
 * @brief 启动舵机PWM
 * @param servo 舵机结构体指针
 */
void Servo_Start(Servo_t *servo) {
    HAL_TIM_PWM_Start(servo->config.htim, servo->config.channel); // 启动PWM输出
}

/**
 * @brief 停止舵机PWM
 * @param servo 舵机结构体指针
 */
void Servo_Stop(Servo_t *servo) {
    HAL_TIM_PWM_Stop(servo->config.htim, servo->config.channel); // 停止PWM输出
}

/**
 * @brief 角度到脉宽值转换
 * @param servo 舵机结构体指针
 * @param angle 目标角度
 * @return 对应的CCR值
 */
static uint32_t Servo_AngleToPulse(Servo_t *servo, float angle) {
    // 限制角度范围
    if (angle < servo->config.min_angle) angle = servo->config.min_angle;
    if (angle > servo->config.max_angle) angle = servo->config.max_angle;
    
    // 线性插值计算CCR值
    float ratio = (angle - servo->config.min_angle) / 
                  (servo->config.max_angle - servo->config.min_angle);
    
    uint32_t pulse = servo->config.min_pulse + 
                     (uint32_t)(ratio * (servo->config.max_pulse - servo->config.min_pulse));
    
    return pulse;
}

/**
 * @brief 设置舵机角度
 * @param servo 舵机结构体指针
 * @param angle 目标角度
 */
void Servo_SetAngle(Servo_t *servo, float angle) {
    uint32_t pulse = Servo_AngleToPulse(servo, angle); // 计算脉宽值
    
    // 设置PWM占空比
    // switch(servo->config.channel) {
    //     case TIM_CHANNEL_1:
    //         __HAL_TIM_SET_COMPARE(servo->config.htim, TIM_CHANNEL_1, pulse);
    //         break;
    //     case TIM_CHANNEL_2:
    //         __HAL_TIM_SET_COMPARE(servo->config.htim, TIM_CHANNEL_2, pulse);
    //         break;
    //         break;
    //     default:
    //         break;
    // }
    __HAL_TIM_SET_COMPARE(servo->config.htim, servo->config.channel, pulse);
    servo->current_angle = angle; // 更新当前角度
}

/**
 * @brief 获取当前角度
 * @param servo 舵机结构体指针
 * @return 当前角度值
 */
float Servo_GetAngle(Servo_t *servo) {
    return servo->current_angle;
}