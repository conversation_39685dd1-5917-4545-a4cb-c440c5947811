#include "app_servo.h"
#include "my_define.h"
#include "math.h"

Servo_t pitch_servo; // 转向舵机
Servo_t yaw_servo;   // 偏航舵机

Servo_Config_t servo_Pitchconfig = {
    .htim      = &htim9,        // 使用TIM3
    .channel   = TIM_CHANNEL_2, // 通道2
    .min_pulse = 124,           // 最小脉宽值(对应0度)
    .max_pulse = 624,           // 最大脉宽值(对应180度)
    .min_angle = 0.0f,          // 最小角度
    .max_angle = 180.0f         // 最大角度
};
Servo_Config_t servo_Yawconfig = {
    .htim      = &htim9,        // 使用TIM3
    .channel   = TIM_CHANNEL_1, // 通道1
    .min_pulse = 124,           // 最小脉宽值(对应0度)
    .max_pulse = 624,           // 最大脉宽值(对应270度)
    .min_angle = 0.0f,          // 最小角度
    .max_angle = 270.0f         // 最大角度
};

void My_Servo_Init(void)
{

  Servo_Init(&pitch_servo, &servo_Pitchconfig);
  Servo_Init(&yaw_servo, &servo_Yawconfig);

  Servo_Start(&pitch_servo);
  Servo_Start(&yaw_servo);

  Servo_SetAngle(&pitch_servo, 90.0f);
  Servo_SetAngle(&yaw_servo, 135.0f);
}

// 假设50ms的周期
/*
 左边界：143. 右边界：117
 下边界：86 上边界 109
*/

#define L_Loction 143
#define R_Loction 117
#define F_Loction 109
#define B_Loction 86

int g_servo_run_flag = 1; // 舵机运行标志
// 步进值配置
#define SERVO_STEP 0.36f // 每步移动角度
static int servo_state = 0; // 当前运动状态
static int servo_delay_cnt = 0; // 延时计数器
static float yaw_target = L_Loction; // yaw目标角度
static float pitch_target = F_Loction; // pitch目标角度
void Servo_Proc()
{
  if (!g_servo_run_flag) return;
  if (++servo_delay_cnt < 2) return; // 步进周期可调
  servo_delay_cnt = 0;

  float yaw_now = Servo_GetAngle(&yaw_servo);
  float pitch_now = Servo_GetAngle(&pitch_servo);
  // 步进到目标角度
  if (fabsf(yaw_now - yaw_target) > SERVO_STEP)
    yaw_now += (yaw_target > yaw_now ? SERVO_STEP : -SERVO_STEP);
  else
    yaw_now = yaw_target;
  if (fabsf(pitch_now - pitch_target) > SERVO_STEP)
    pitch_now += (pitch_target > pitch_now ? SERVO_STEP : -SERVO_STEP);
  else
    pitch_now = pitch_target;
  Servo_SetAngle(&yaw_servo, yaw_now);
  Servo_SetAngle(&pitch_servo, pitch_now);

  // 到达目标后切换下一个状态
  if (yaw_now == yaw_target && pitch_now == pitch_target) {
    switch (servo_state) {
      case 0: // 从中间到左端点
        yaw_target = L_Loction;
        pitch_target = F_Loction;
        servo_state = 1;
        break;
      case 1: // 左端点 -> 右端点
        yaw_target = R_Loction;
        pitch_target = F_Loction;
        servo_state = 2;
        break;
      case 2: // 右 -> 下
        yaw_target = R_Loction;
        pitch_target = B_Loction;
        servo_state = 3;
        break;
      case 3: // 下 -> 左
        yaw_target = L_Loction;
        pitch_target = B_Loction;
        servo_state = 4;
        break;
      case 4: // 左 -> 上
        yaw_target = L_Loction;
        pitch_target = F_Loction;
        servo_state = 0;
        break;
      default:
        servo_state = 0;
        break;
    }
  }
}


