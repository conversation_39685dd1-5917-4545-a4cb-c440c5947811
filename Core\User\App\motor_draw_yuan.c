#include "my_define.h"
#include "math.h"

// --- 参数定义 ---
#define CIRCLE_RADIUS_STEPS  10// 定义圆的半径，单位：步
#define NUM_PATH_POINTS      50 // 将圆分解成100个点，点越多越平滑
#define DELAY_BETWEEN_POINTS 20  // 每个点之间的延时(ms)，控制速度

// --- 路径点存储数组 ---
int16_t path_x[NUM_PATH_POINTS];
int16_t path_y[NUM_PATH_POINTS];

// --- 当前电机位置 (单位：步) ---
long current_motor_pos_x = 0;
long current_motor_pos_y = 0;

// --- 假设你已有的驱动函数 ---
// 在 main.c 的 /* USER CODE BEGIN 4 */ 区域添加
void calculate_circle_path()
{
  for (int i = 0; i < NUM_PATH_POINTS; i++) {
    // 计算当前点的角度 (弧度)
    float angle = (float)i / NUM_PATH_POINTS * 2.0f * 3.14159f;

    // 使用三角函数计算坐标，并四舍五入为整数步数
    path_x[i] = (int16_t)(CIRCLE_RADIUS_STEPS * cos(angle));
    path_y[i] = (int16_t)(CIRCLE_RADIUS_STEPS * sin(angle));
  }
}

void move_to_target(long target_x, long target_y)
{
  long steps_to_move_x = target_x - current_motor_pos_x;
  long steps_to_move_y = target_y - current_motor_pos_y;

  Emm_V5_Pos_Control(&MOTOR_UART, 1, 0, 5, 0, steps_to_move_x, 1,
                     0); // 位置模式
  Emm_V5_Pos_Control(&MOTOR_UART, 1, 0, 5, 0, steps_to_move_y, 1,
                     0); // 位置模式
  // 更新当前位置
  current_motor_pos_x = target_x;
  current_motor_pos_y = target_y;
}

