#include "my_define.h"

#define DEBUG

/* PID 全局结构体 */
PID_Controller pid_x, pid_y;

/* PID初始化 */
void Pid_Init(void)
{
  PID_Init(&pid_x, 0.6, 0, 0, MOTOR_MAX_ANGLE, MOTOR_MAX_ANGLE);
  PID_Init(&pid_y, 0.6, 0, 0, MOTOR_MAX_ANGLE, MOTOR_MAX_ANGLE);
}

/* 内部函数：用于判断点之间的距离 */
static uint8_t is_arrive_target(uint32_t current_x, uint32_t current_y,
                                uint32_t target_x, uint32_t target_y,
                                uint8_t threshold)
{
  /* 判断当前位置是否到达目标位置 */
  if (abs((uint32_t)(current_x - target_x)) <= threshold &&
      abs((uint32_t)(current_y - target_y)) <= threshold) {
    return 1; // 到达目标位置
  }
  return 0; // 未到达目标位置
}

volatile uint8_t is_get_loction = 0; // 是否定位
volatile uint8_t is_get_lunkuo  = 0; // 是否获取轮廓点
uint32_t pid_target_index       = 0; // PID目标索引
uint8_t is_xunji_done           = 1; // 是否完成巡线

void Pid_Luolun(void)
{
  static uint32_t target_x = 0, target_y = 0; // 静态目标坐标 #状态保持
  static uint32_t retry_count = 0; // 重试计数 #超时处理
  
  // 检查轮廓点数据有效性 #安全检查
  if (is_get_lunkuo != 1) return;

  Uart_Trig_To_Maixcam(RETURN_RED_LASER_POINT); // 触发视觉获取
  HAL_Delay(10); // 等待响应 #时序优化
  
  /* 等待获取激光点位置 */
  while (is_get_loction == 0) {
    HAL_Delay(10);
    if (++retry_count > 10) {
      Uart_Trig_To_Maixcam(RETURN_RED_LASER_POINT); // 重新触发 #容错
      retry_count = 0;
    }
    if (retry_count > 100) return; // 超时退出 #防死锁
  }
  
  is_get_loction = 0; // 重置标志位
  retry_count = 0; // 重置计数器
  uint32_t current_x = laser_point[0][0];
  uint32_t current_y = laser_point[0][1];

  /* 目标管理逻辑 */
  if (is_xunji_done == 1 && target_x == 0 && target_y == 0) {
    target_x = lunkuo_point[pid_target_index][0]; // 初始化目标 #首次设置
    target_y = lunkuo_point[pid_target_index][1];
    is_xunji_done = 0;
  }
  
  /* 到达判断与目标切换 */
  if (is_arrive_target(current_x, current_y, target_x, target_y, 4)) {
    pid_target_index++;
    if (pid_target_index >= MAIXCAM_LUNKUO_POINTS) {
      pid_target_index = 0; // 循环追踪 #重置
      is_xunji_done = 1;
      return; // 完成一轮追踪 #退出
    }
    target_x = lunkuo_point[pid_target_index][0]; // 更新目标 #切换点
    target_y = lunkuo_point[pid_target_index][1];
  }

  /* PID计算 */
  float pid_out_x = PID_Calculate(&pid_x, target_x, current_x);
  float pid_out_y = PID_Calculate(&pid_y, target_y, current_y);
  int32_t max_pid = (abs(pid_out_x) > abs(pid_out_y)) ? abs(pid_out_x) : abs(pid_out_y);
  
  if (max_pid == 0) return; // 防除零 #安全
  
  /* 速度优化分配 */
  int32_t Vx = (MOTOR_MAX_SPEED * abs(pid_out_x)) / max_pid; // 比例速度 #协调运动
  int32_t Vy = (MOTOR_MAX_SPEED * abs(pid_out_y)) / max_pid;

#ifdef DEBUG
  my_printf(&huart1, "Target[%d]: (%d,%d) PID: %.2f,%.2f\n", 
            pid_target_index, target_x, target_y, pid_out_x, pid_out_y);
#endif

  /* 电机控制执行 */
  if (pid_out_x > 0) {
    Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_X_ADDR, 1, Vx, MOTOR_ACCEL, 
                       (uint16_t)pid_out_x, false, false);
  } else if (pid_out_x < 0) {
    Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_X_ADDR, 0, Vx, MOTOR_ACCEL, 
                       (uint16_t)(-pid_out_x), false, false);
  }
  
  HAL_Delay(1);
  if (pid_out_y > 0) {
    Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_Y_ADDR, 1, Vy, MOTOR_ACCEL, 
                       (uint16_t)pid_out_y, false, false);
  } else if (pid_out_y < 0) {
    Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_Y_ADDR, 0, Vy, MOTOR_ACCEL, 
                       (uint16_t)(-pid_out_y), false, false);
  }
  
  HAL_Delay(1);
}

/* 测试程序，用来调节PID */
uint32_t test_index = 0;
void Pid_Luolun_Test(uint32_t target_x, uint32_t target_y)
{
  while (1) {
    Uart_Trig_To_Maixcam(RETURN_RED_LASER_POINT); // 触发视觉获取红色激光笔点
    HAL_Delay(10);                                // 等待10ms
    /* 等待获取红色激光笔点 */
    while (is_get_loction == 0) {
      HAL_Delay(10); // 等待10ms
      if (++test_index > 10) {
        Uart_Trig_To_Maixcam(
          RETURN_RED_LASER_POINT); // 触发视觉获取红色激光笔点
      }
      if (test_index > 100) break;
    }
    /* 获取当前位置 */
    is_get_loction     = 0; // 重置获取位置标志位
    uint32_t current_x = laser_point[0][0];
    uint32_t current_y = laser_point[0][1];

    /* 计算pid */
    float pid_out_x = PID_Calculate(&pid_x, target_x, current_x);
    float pid_out_y = PID_Calculate(&pid_y, target_y, current_y);
    int32_t max_pid = (abs(pid_out_x) > abs(pid_out_y)) ? abs(pid_out_x) : abs(pid_out_y);

    /* 优化速度 */
    int32_t Vx = (MOTOR_MAX_SPEED * abs(pid_out_x)) / max_pid; // 比例速度 #优化
    int32_t Vy = (MOTOR_MAX_SPEED * abs(pid_out_y)) / max_pid;
#ifdef DEBUG
    my_printf(&huart1, "PID X: %.2f, Y: %.2f\n", pid_out_x, pid_out_y);
#endif
    /***********************************************************
     *  执行
     ************************************************************/
    // 往右边移动 -> 顺时针
    if (pid_out_x > 0) {
      Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_X_ADDR, 1, Vx,
                         MOTOR_ACCEL, (uint16_t)pid_out_x, false, false);
    } else if (pid_out_x < 0) {
      Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_X_ADDR, 0, Vx,
                         MOTOR_ACCEL, (uint16_t)(-pid_out_x), false, false);
    }
    HAL_Delay(1); // 延时1ms，避免过快执行
    if (pid_out_y > 0) {
      Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_Y_ADDR, 1, Vy,
                         MOTOR_ACCEL, (uint16_t)pid_out_y, false, false);
    } else if (pid_out_y < 0) {
      Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_Y_ADDR, 0, Vy,
                         MOTOR_ACCEL, (uint16_t)(-pid_out_y), false, false);
    }
    /* 串口退出程序 */
    if (uart1_rx_buffer[0] == 'e') break;
    HAL_Delay(500);
  }
  /* 多机同步 */
  // HAL_Delay(1);
  // Emm_V5_Synchronous_motion(&MOTOR_UART, 0);
}

// static void Pixel_to_Step()
// {
// }

/**
 * @brief 走直线优化版 #插补算法
 */
void Go_Line(int32_t current_x, int32_t current_y, int32_t target_x,
             int32_t target_y, int32_t num)
{
  if (num <= 0) return; // 防护检查 #安全

  int32_t error_x  = target_x - current_x,
          error_y  = target_y - current_y; // 总误差 #简化
  int32_t delta_x  = error_x / num,
          delta_y  = error_y / num; // 每步位移 #均匀分割
  int32_t remain_x = error_x % num, remain_y = error_y % num; // 余数处理 #精度

  // 速度分配优化 #比例控制
  int32_t Vx, Vy;
  int32_t max_error =
    (abs(error_x) > abs(error_y)) ? abs(error_x) : abs(error_y);
  if (max_error == 0) return;                        // 已到达目标 #提前退出
  Vx = (MOTOR_MAX_SPEED * abs(error_x)) / max_error; // 比例速度 #优化
  Vy = (MOTOR_MAX_SPEED * abs(error_y)) / max_error;

  // 插补移动 #循环优化
  for (int i = 1; i <= num; i++) {
    // 真正的余数均匀分配 # 布雷森汉姆算法思想
    int32_t step_x =
      delta_x + ((abs(remain_x) > 0 &&
                  i * abs(remain_x) / num > (i - 1) * abs(remain_x) / num)
                   ? (remain_x >= 0 ? 1 : -1)
                   : 0);
    int32_t step_y =
      delta_y + ((abs(remain_y) > 0 &&
                  i * abs(remain_y) / num > (i - 1) * abs(remain_y) / num)
                   ? (remain_y >= 0 ? 1 : -1)
                   : 0);

    if (step_x != 0) { // X轴有移动 #效率
      Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_X_ADDR, (step_x >= 0) ? 0 : 1, Vx,
                         MOTOR_ACCEL, (uint16_t)abs(step_x), false,
                         false); // 相对移动
    }
    HAL_Delay(1);
    if (step_y != 0) { // Y轴有移动 #效率
      Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_Y_ADDR, (step_y >= 0) ? 0 : 1, Vy,
                         MOTOR_ACCEL, (uint16_t)abs(step_y), false, false);
    }
    HAL_Delay(200); // 等待完成 #时序
  }
}

/* 追激光部分 */
/* 摄像头传来两者差值，根据差值追踪 */
void Pid_zhuizong_Laser()
{

}