#MicroXplorer Configuration settings - do not modify
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_10
ADC1.IPParameters=Rank-1\#ChannelRegularConversion,master,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,NbrOfConversionFlag
ADC1.NbrOfConversionFlag=1
ADC1.Rank-1\#ChannelRegularConversion=1
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.master=1
ADC2.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_7
ADC2.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,NbrOfConversionFlag
ADC2.NbrOfConversionFlag=1
ADC2.Rank-0\#ChannelRegularConversion=1
ADC2.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.I2C1_TX.0.Direction=DMA_MEMORY_TO_PERIPH
Dma.I2C1_TX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.I2C1_TX.0.Instance=DMA1_Stream6
Dma.I2C1_TX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.I2C1_TX.0.MemInc=DMA_MINC_ENABLE
Dma.I2C1_TX.0.Mode=DMA_NORMAL
Dma.I2C1_TX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.I2C1_TX.0.PeriphInc=DMA_PINC_DISABLE
Dma.I2C1_TX.0.Priority=DMA_PRIORITY_LOW
Dma.I2C1_TX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=I2C1_TX
Dma.Request1=USART1_RX
Dma.Request2=UART5_RX
Dma.RequestsNb=3
Dma.UART5_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART5_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART5_RX.2.Instance=DMA1_Stream0
Dma.UART5_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART5_RX.2.MemInc=DMA_MINC_ENABLE
Dma.UART5_RX.2.Mode=DMA_NORMAL
Dma.UART5_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART5_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.UART5_RX.2.Priority=DMA_PRIORITY_LOW
Dma.UART5_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART1_RX.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.1.Instance=DMA2_Stream2
Dma.USART1_RX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.1.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.1.Mode=DMA_NORMAL
Dma.USART1_RX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.1.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C1.I2C_Mode=I2C_Fast
I2C1.IPParameters=I2C_Mode
KeepUserPlacement=false
Mcu.CPN=STM32F407VET6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=ADC2
Mcu.IP10=TIM9
Mcu.IP11=UART5
Mcu.IP12=USART1
Mcu.IP13=USART3
Mcu.IP2=DMA
Mcu.IP3=I2C1
Mcu.IP4=I2C2
Mcu.IP5=NVIC
Mcu.IP6=RCC
Mcu.IP7=SYS
Mcu.IP8=TIM2
Mcu.IP9=TIM3
Mcu.IPNb=14
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PA3
Mcu.Pin11=PA4
Mcu.Pin12=PA5
Mcu.Pin13=PA6
Mcu.Pin14=PA7
Mcu.Pin15=PC4
Mcu.Pin16=PC5
Mcu.Pin17=PB0
Mcu.Pin18=PB1
Mcu.Pin19=PE7
Mcu.Pin2=PE4
Mcu.Pin20=PE8
Mcu.Pin21=PE9
Mcu.Pin22=PE10
Mcu.Pin23=PE11
Mcu.Pin24=PE12
Mcu.Pin25=PE13
Mcu.Pin26=PE14
Mcu.Pin27=PE15
Mcu.Pin28=PB10
Mcu.Pin29=PB11
Mcu.Pin3=PE5
Mcu.Pin30=PB12
Mcu.Pin31=PB13
Mcu.Pin32=PB14
Mcu.Pin33=PB15
Mcu.Pin34=PD8
Mcu.Pin35=PD9
Mcu.Pin36=PD10
Mcu.Pin37=PD12
Mcu.Pin38=PD13
Mcu.Pin39=PD14
Mcu.Pin4=PE6
Mcu.Pin40=PD15
Mcu.Pin41=PC6
Mcu.Pin42=PC7
Mcu.Pin43=PA9
Mcu.Pin44=PA10
Mcu.Pin45=PA13
Mcu.Pin46=PA14
Mcu.Pin47=PA15
Mcu.Pin48=PC10
Mcu.Pin49=PC11
Mcu.Pin5=PC13-ANTI_TAMP
Mcu.Pin50=PC12
Mcu.Pin51=PD0
Mcu.Pin52=PD1
Mcu.Pin53=PD2
Mcu.Pin54=PD4
Mcu.Pin55=PD5
Mcu.Pin56=PD7
Mcu.Pin57=PB3
Mcu.Pin58=PB4
Mcu.Pin59=PB5
Mcu.Pin6=PC0
Mcu.Pin60=PB6
Mcu.Pin61=PB7
Mcu.Pin62=PE0
Mcu.Pin63=PE1
Mcu.Pin64=VP_SYS_VS_Systick
Mcu.Pin7=PC1
Mcu.Pin8=PA0-WKUP
Mcu.Pin9=PA2
Mcu.PinsNb=65
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VETx
MxCube.Version=6.14.0
MxDb.Version=DB.6.0.140
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream6_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:true\:false\:true\:false\:true\:false
NVIC.UART5_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.GPIOParameters=GPIO_Label
PA0-WKUP.GPIO_Label=Motor_PWMA
PA0-WKUP.Locked=true
PA0-WKUP.Signal=S_TIM5_CH1
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA15.Signal=S_TIM2_CH1_ETR
PA2.Locked=true
PA2.Signal=S_TIM9_CH1
PA3.Signal=S_TIM9_CH2
PA4.GPIOParameters=GPIO_Speed,GPIO_Label
PA4.GPIO_Label=AD0
PA4.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA4.Locked=true
PA4.Signal=GPIO_Output
PA5.GPIOParameters=GPIO_Speed,GPIO_Label
PA5.GPIO_Label=AD1
PA5.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA5.Locked=true
PA5.Signal=GPIO_Output
PA6.GPIOParameters=GPIO_Speed,GPIO_Label
PA6.GPIO_Label=AD2
PA6.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA6.Locked=true
PA6.Signal=GPIO_Output
PA7.GPIOParameters=GPIO_Label
PA7.GPIO_Label=Hw_OUT
PA7.Locked=true
PA7.Signal=ADCx_IN7
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.GPIOParameters=GPIO_Label
PB0.GPIO_Label=BEEP
PB0.Locked=true
PB0.Signal=GPIO_Output
PB1.GPIOParameters=GPIO_Label
PB1.GPIO_Label=LED0
PB1.Locked=true
PB1.Signal=GPIO_Output
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.Locked=true
PB13.Signal=GPIO_Output
PB14.Locked=true
PB14.Signal=GPIO_Output
PB15.Locked=true
PB15.Signal=GPIO_Output
PB3.Signal=S_TIM2_CH2
PB4.GPIOParameters=GPIO_Label
PB4.GPIO_Label=BIN2
PB4.Locked=true
PB4.Signal=GPIO_Output
PB5.GPIOParameters=GPIO_Label
PB5.GPIO_Label=BIN1
PB5.Locked=true
PB5.Signal=GPIO_Output
PB6.GPIOParameters=GPIO_Label
PB6.GPIO_Label=OLED_SCL
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.GPIOParameters=GPIO_Label
PB7.GPIO_Label=OLED_SDA
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PC0.Locked=true
PC0.Signal=ADCx_IN10
PC1.Locked=true
PC1.Signal=ADCx_IN11
PC10.GPIOParameters=GPIO_Label
PC10.GPIO_Label=IMU_TX
PC10.Mode=Asynchronous
PC10.Signal=USART3_TX
PC11.GPIOParameters=GPIO_Label
PC11.GPIO_Label=IMU_RX
PC11.Mode=Asynchronous
PC11.Signal=USART3_RX
PC12.Mode=Asynchronous
PC12.Signal=UART5_TX
PC13-ANTI_TAMP.GPIOParameters=GPIO_Label
PC13-ANTI_TAMP.GPIO_Label=LED1
PC13-ANTI_TAMP.Locked=true
PC13-ANTI_TAMP.Signal=GPIO_Output
PC4.GPIOParameters=GPIO_Label
PC4.GPIO_Label=Hw_EN
PC4.Locked=true
PC4.Signal=GPIO_Output
PC5.Locked=true
PC5.Signal=GPIO_Output
PC6.GPIOParameters=GPIO_Label
PC6.GPIO_Label=Encoder_A2
PC6.Signal=S_TIM3_CH1
PC7.GPIOParameters=GPIO_Label
PC7.GPIO_Label=Encoder_B2
PC7.Signal=S_TIM3_CH2
PD0.Locked=true
PD0.Signal=GPIO_Output
PD1.Locked=true
PD1.Signal=GPIO_Output
PD10.Locked=true
PD10.Signal=GPIO_Output
PD12.Locked=true
PD12.Signal=GPIO_Output
PD13.Locked=true
PD13.Signal=GPIO_Output
PD14.Locked=true
PD14.Signal=GPIO_Output
PD15.Locked=true
PD15.Signal=GPIO_Output
PD2.Locked=true
PD2.Mode=Asynchronous
PD2.Signal=UART5_RX
PD4.Locked=true
PD4.Signal=GPIO_Output
PD5.Locked=true
PD5.Signal=GPIO_Output
PD7.Locked=true
PD7.Signal=GPIO_Output
PD8.Locked=true
PD8.Signal=GPIO_Output
PD9.Locked=true
PD9.Signal=GPIO_Output
PE0.GPIOParameters=GPIO_Label
PE0.GPIO_Label=AIN2
PE0.Locked=true
PE0.Signal=GPIO_Output
PE1.GPIOParameters=GPIO_Label
PE1.GPIO_Label=AIN1
PE1.Locked=true
PE1.Signal=GPIO_Output
PE10.Locked=true
PE10.Signal=GPIO_Output
PE11.Locked=true
PE11.Signal=GPIO_Output
PE12.Locked=true
PE12.Signal=GPIO_Output
PE13.Locked=true
PE13.Signal=GPIO_Output
PE14.Locked=true
PE14.Signal=GPIO_Output
PE15.Locked=true
PE15.Signal=GPIO_Output
PE2.GPIOParameters=GPIO_PuPd,GPIO_Label
PE2.GPIO_Label=Key2
PE2.GPIO_PuPd=GPIO_PULLUP
PE2.Locked=true
PE2.Signal=GPIO_Input
PE3.GPIOParameters=GPIO_PuPd,GPIO_Label
PE3.GPIO_Label=Key1
PE3.GPIO_PuPd=GPIO_PULLUP
PE3.Locked=true
PE3.Signal=GPIO_Input
PE4.GPIOParameters=GPIO_PuPd,GPIO_Label
PE4.GPIO_Label=Key0
PE4.GPIO_PuPd=GPIO_PULLUP
PE4.Locked=true
PE4.Signal=GPIO_Input
PE5.GPIOParameters=GPIO_PuPd,GPIO_Label
PE5.GPIO_Label=LED_RED
PE5.GPIO_PuPd=GPIO_PULLUP
PE5.Locked=true
PE5.Signal=GPIO_Output
PE6.GPIOParameters=GPIO_PuPd,GPIO_Label
PE6.GPIO_Label=LED_GREEN
PE6.GPIO_PuPd=GPIO_PULLUP
PE6.Locked=true
PE6.Signal=GPIO_Output
PE7.Locked=true
PE7.Signal=GPIO_Output
PE8.Locked=true
PE8.Signal=GPIO_Output
PE9.Locked=true
PE9.Signal=GPIO_Output
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=Hw_car.ioc
ProjectManager.ProjectName=Hw_car
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_I2C1_Init-I2C1-false-HAL-true,5-MX_I2C2_Init-I2C2-false-HAL-true,6-MX_TIM3_Init-TIM3-false-HAL-true,7-MX_USART1_UART_Init-USART1-false-HAL-true,8-MX_USART3_UART_Init-USART3-false-HAL-true,9-MX_ADC2_Init-ADC2-false-HAL-true,10-MX_UART5_Init-UART5-false-HAL-true,11-MX_ADC1_Init-ADC1-false-HAL-true,12-MX_TIM2_Init-TIM2-false-HAL-true,13-MX_TIM9_Init-TIM9-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=8
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=12500000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.ADCx_IN10.0=ADC1_IN10,IN10
SH.ADCx_IN10.ConfNb=1
SH.ADCx_IN11.0=ADC1_IN11,IN11
SH.ADCx_IN11.ConfNb=1
SH.ADCx_IN7.0=ADC2_IN7,IN7
SH.ADCx_IN7.ConfNb=1
SH.S_TIM2_CH1_ETR.0=TIM2_CH1,Encoder_Interface
SH.S_TIM2_CH1_ETR.ConfNb=1
SH.S_TIM2_CH2.0=TIM2_CH2,Encoder_Interface
SH.S_TIM2_CH2.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Encoder_Interface
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,Encoder_Interface
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM5_CH1.0=TIM5_CH1
SH.S_TIM5_CH1.ConfNb=1
SH.S_TIM9_CH1.0=TIM9_CH1,PWM Generation1 CH1
SH.S_TIM9_CH1.ConfNb=1
SH.S_TIM9_CH2.0=TIM9_CH2,PWM Generation2 CH2
SH.S_TIM9_CH2.ConfNb=1
TIM2.IC1Filter=6
TIM2.IC2Filter=6
TIM2.IPParameters=IC1Filter,IC2Filter
TIM3.IC1Filter=6
TIM3.IC2Filter=6
TIM3.IPParameters=IC2Filter,IC1Filter
TIM9.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM9.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM9.IPParameters=Channel-PWM Generation1 CH1,Channel-PWM Generation2 CH2,Prescaler,Period
TIM9.Period=4999
TIM9.Prescaler=672 - 1
UART5.IPParameters=VirtualMode
UART5.VirtualMode=Asynchronous
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
