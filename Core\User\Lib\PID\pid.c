#include "my_define.h"


// 初始化PID控制器
void PID_Init(PID_Controller *pid, float kp, float ki, float kd, float max_i, float max_out) {
    pid->Kp = kp;
    pid->Ki = ki;
    pid->Kd = kd;
    pid->error = 0;
    pid->last_error = 0;
    pid->integral = 0;
    pid->derivative = 0;
    pid->max_integral = max_i;
    pid->max_output = max_out;
}

// PID计算  target --- 目标值    current --- 当前值
float PID_Calculate(PID_Controller *pid, float target, float current) {
    // 计算误差
    pid->error = target - current;
    
    // 积分项
    pid->integral += pid->error;
    
    // 积分限幅
    if(pid->integral > pid->max_integral)
        pid->integral = pid->max_integral;
    else if(pid->integral < -pid->max_integral)
        pid->integral = -pid->max_integral;
    
    // 微分项
    pid->derivative = pid->error - pid->last_error;
    pid->last_error = pid->error;
    
    // PID输出
    pid->output = pid->Kp * pid->error + pid->Ki * pid->integral + pid->Kd * pid->derivative;
    
    // 输出限幅
    if(pid->output > pid->max_output)
        pid->output = pid->max_output;
    else if(pid->output < -pid->max_output)
        pid->output = -pid->max_output;
    
    return pid->output;
}

