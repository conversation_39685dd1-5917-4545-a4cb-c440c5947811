/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define Key2_Pin GPIO_PIN_2
#define Key2_GPIO_Port GPIOE
#define Key1_Pin GPIO_PIN_3
#define Key1_GPIO_Port GPIOE
#define Key0_Pin GPIO_PIN_4
#define Key0_GPIO_Port GPIOE
#define LED_RED_Pin GPIO_PIN_5
#define LED_RED_GPIO_Port GPIOE
#define LED_GREEN_Pin GPIO_PIN_6
#define LED_GREEN_GPIO_Port GPIOE
#define LED1_Pin GPIO_PIN_13
#define LED1_GPIO_Port GPIOC
#define Motor_PWMA_Pin GPIO_PIN_0
#define Motor_PWMA_GPIO_Port GPIOA
#define AD0_Pin GPIO_PIN_4
#define AD0_GPIO_Port GPIOA
#define AD1_Pin GPIO_PIN_5
#define AD1_GPIO_Port GPIOA
#define AD2_Pin GPIO_PIN_6
#define AD2_GPIO_Port GPIOA
#define Hw_OUT_Pin GPIO_PIN_7
#define Hw_OUT_GPIO_Port GPIOA
#define Hw_EN_Pin GPIO_PIN_4
#define Hw_EN_GPIO_Port GPIOC
#define BEEP_Pin GPIO_PIN_0
#define BEEP_GPIO_Port GPIOB
#define LED0_Pin GPIO_PIN_1
#define LED0_GPIO_Port GPIOB
#define Encoder_A2_Pin GPIO_PIN_6
#define Encoder_A2_GPIO_Port GPIOC
#define Encoder_B2_Pin GPIO_PIN_7
#define Encoder_B2_GPIO_Port GPIOC
#define IMU_TX_Pin GPIO_PIN_10
#define IMU_TX_GPIO_Port GPIOC
#define IMU_RX_Pin GPIO_PIN_11
#define IMU_RX_GPIO_Port GPIOC
#define BIN2_Pin GPIO_PIN_4
#define BIN2_GPIO_Port GPIOB
#define BIN1_Pin GPIO_PIN_5
#define BIN1_GPIO_Port GPIOB
#define OLED_SCL_Pin GPIO_PIN_6
#define OLED_SCL_GPIO_Port GPIOB
#define OLED_SDA_Pin GPIO_PIN_7
#define OLED_SDA_GPIO_Port GPIOB
#define AIN2_Pin GPIO_PIN_0
#define AIN2_GPIO_Port GPIOE
#define AIN1_Pin GPIO_PIN_1
#define AIN1_GPIO_Port GPIOE

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
