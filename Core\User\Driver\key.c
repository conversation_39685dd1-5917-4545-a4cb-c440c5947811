#include "key.h"
#include "ebtn.h"
#include "main.h"
#include "usart.h"
#include "my_define.h"

/* 1. 定义按键参数实例 */
// 参数宏: EBTN_PARAMS_INIT(
//     按下消抖时间ms, 释放消抖时间ms,
//     单击有效最短按下时间ms, 单击有效最长按下时间ms,
//     多次单击最大间隔时间ms,
//     长按(KeepAlive)事件周期ms (0禁用),
//     最大连续有效点击次数 (e.g., 1=单击, 2=双击, ...)
// )
const ebtn_btn_param_t key_param_normal = EBTN_PARAMS_INIT(
    20,  // time_debounce: 按下稳定 20ms
    20,  // time_debounce_release: 释放稳定 20ms
    50,  // time_click_pressed_min: 最短单击按下 50ms
    500, // time_click_pressed_max: 最长单击按下 500ms (超过则不算单击)
    300, // time_click_multi_max: 多次单击最大间隔 300ms (两次点击间隔超过则重新计数)
    500, // time_keepalive_period: 长按事件周期 500ms (按下超过 500ms 后，每 500ms 触发一次)
    5    // max_consecutive: 最多支持 5 连击
);

/* 2. 定义静态按键列表 */
// 宏: EBTN_BUTTON_INIT(按键ID, 参数指针)
ebtn_btn_t static_buttons[] = {
    EBTN_BUTTON_INIT(1, &key_param_normal), // KEY1, ID=1, 使用 'key_param_normal' 参数
                                            // EBTN_BUTTON_INIT(2, &key_param_normal), // KEY2, ID=2, 也使用 'key_param_normal' 参数
};

/* 3. 定义静态组合按键列表 (可选) */
// 宏: EBTN_BUTTON_COMBO_INIT(按键ID, 参数指针)
// ebtn_btn_combo_t static_combos[] = {
//     // 假设 KEY1+KEY2 组合键
//     EBTN_BUTTON_COMBO_INIT(101, &key_param_normal), // 组合键, ID=101 (必须与普通按键ID不同)
// };

/* 1. 实现获取按键状态的回调函数 */
// 函数原型: uint8_t (*ebtn_get_state_fn)(struct ebtn_btn *btn);
uint8_t my_get_key_state(struct ebtn_btn *btn)
{
  // 根据传入的按钮实例中的 key_id 判断是哪个物理按键
  switch (btn->key_id) {
    case 1: // 请求读取 KEY1 的状态
      // 假设 KEY1 接在 PB0，按下为低电平 (返回 1 代表按下)
      return (HAL_GPIO_ReadPin(Key2_GPIO_Port, Key2_Pin) == GPIO_PIN_RESET);
  }
  // 注意：返回值 1 表示 "活动/按下"，0 表示 "非活动/释放"
}

// void my_printf(UART_HandleTypeDef* huart, const char* format, ...);

/* 声明变量  */
extern int g_servo_run_flag;
extern Servo_t pitch_servo; // 转向舵机
extern Servo_t yaw_servo;   // 偏航舵机
/* 2. 实现处理按键事件的回调函数 */
// 函数原型: void (*ebtn_evt_fn)(struct ebtn_btn *btn, ebtn_evt_t evt);
void my_handle_key_event(struct ebtn_btn *btn, ebtn_evt_t evt)
{
  uint16_t key_id    = btn->key_id;               // 获取触发事件的按键 ID
  uint16_t click_cnt = ebtn_click_get_count(btn); // 获取连击次数 (仅在 ONCLICK 事件时有意义)
  // uint16_t kalive_cnt = ebtn_keepalive_get_count(btn); // 获取长按计数 (仅在 KEEPALIVE 事件时有意义)

  // 调试打印 (可选)
  // printf("Key ID: %d, Event: %d", key_id, evt);

  // 根据事件类型进行处理
  switch (evt) {

    case EBTN_EVT_ONCLICK: // 单击/连击事件 (在释放后，或达到最大连击数，或超时后触发)

      // printf(" - Clicked (%d times)\n", click_cnt);
      // --- 根据 key_id 和 click_cnt 执行不同操作 ---
      if (key_id == 1) { // 如果是 KEY1 触发的 CLICK
        if (click_cnt == 1) {
          // my_printf(&huart1, "KEY1 Single Click - Toggle LED1\r\n");
          if (g_servo_run_flag == 0) {
            g_servo_run_flag = 1;
          } else {
            g_servo_run_flag = 0;

            // 发送当前角度
            my_printf(&huart1, "Yaw Servo Angle: %.2f\r\n", yaw_servo.current_angle);
            my_printf(&huart1, "Pitch Servo Angle: %.2f\r\n", pitch_servo.current_angle);
          }
        }
      }
      break;
    default: // 未知事件 (理论上不应发生)
      // printf(" - Unknown Event\n");
      break;
  }
}

void Key_init(void)
{
  // 初始化按键库
  ebtn_init(static_buttons,
            EBTN_ARRAY_SIZE(static_buttons),
            NULL,
            0,
            my_get_key_state,
            my_handle_key_event); // 无组合按键
}

void Key_Proc(void)
{
  ebtn_process(HAL_GetTick());
}