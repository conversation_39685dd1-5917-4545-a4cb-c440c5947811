#ifndef __PID_H
#define __PID_H

// PID控制结构体
typedef struct {
    float Kp;           // 比例系数
    float Ki;           // 积分系数
    float Kd;           // 微分系数
    float error;        // 当前误差
    float last_error;   // 上次误差
    float integral;     // 积分项
    float derivative;   // 微分项
    float output;       // 输出
    float max_integral; // 积分限幅
    float max_output;   // 输出限幅
} PID_Controller;
/****初始化PID控制器****/
void PID_Init(PID_Controller *pid, float kp, float ki, float kd, float max_i, float max_out);
/****PID计算  target --- 目标值  current --- 当前值****/
float PID_Calculate(PID_Controller *pid, float target, float current);

#endif
